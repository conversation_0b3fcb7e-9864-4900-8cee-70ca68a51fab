package com.app.gamehub.service;

import com.app.gamehub.dto.GrabPositionRequest;
import com.app.gamehub.dto.SetPositionGrabTimeRequest;
import com.app.gamehub.entity.*;
import com.app.gamehub.exception.BusinessException;
import com.app.gamehub.repository.*;
import com.app.gamehub.util.UserContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;


import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PositionGrabServiceTest {

    @Mock
    private DynastyRepository dynastyRepository;
    
    @Mock
    private DynastyPositionRepository dynastyPositionRepository;
    
    @Mock
    private PositionGrabRepository positionGrabRepository;
    
    @Mock
    private GameAccountRepository gameAccountRepository;

    @InjectMocks
    private PositionGrabService positionGrabService;

    private Long userId = 1L;
    private Long dynastyId = 1L;
    private Long accountId = 1L;

    @BeforeEach
    void setUp() {
        // 模拟用户上下文
        try (var mockedStatic = mockStatic(UserContext.class)) {
            mockedStatic.when(UserContext::getUserId).thenReturn(userId);
        }
    }

    @Test
    void setPositionGrabTime_Success() {
        // Given
        SetPositionGrabTimeRequest request = new SetPositionGrabTimeRequest();
        request.setPositionType(PositionType.TAI_WEI);
        request.setGrabStartTime(LocalDateTime.of(2025, 8, 11, 10, 0, 0));
        request.setGrabEndTime(LocalDateTime.of(2025, 8, 11, 23, 0, 0));
        request.setDutyDate(LocalDate.of(2025, 8, 12));
        request.setDisabledTimeSlots(Arrays.asList(0, 1, 2));

        Dynasty dynasty = new Dynasty();
        dynasty.setId(dynastyId);
        dynasty.setEmperorId(userId);

        DynastyPosition position = new DynastyPosition();
        position.setId(1L);
        position.setDynastyId(dynastyId);
        position.setPositionType(PositionType.TAI_WEI);

        when(dynastyRepository.findById(dynastyId)).thenReturn(Optional.of(dynasty));
        when(dynastyPositionRepository.findByDynastyIdAndPositionType(dynastyId, PositionType.TAI_WEI))
                .thenReturn(Optional.of(position));
        when(dynastyPositionRepository.save(any(DynastyPosition.class))).thenReturn(position);

        // When
        try (var mockedStatic = mockStatic(UserContext.class)) {
            mockedStatic.when(UserContext::getUserId).thenReturn(userId);
            DynastyPosition result = positionGrabService.setPositionGrabTime(dynastyId, request);

            // Then
            assertNotNull(result);
            verify(dynastyPositionRepository).save(position);
            verify(positionGrabRepository).deleteByDutyDateBefore(request.getDutyDate().minusDays(2));
        }
    }

    @Test
    void setPositionGrabTime_InvalidTimeRange_ThrowsException() {
        // Given
        SetPositionGrabTimeRequest request = new SetPositionGrabTimeRequest();
        request.setPositionType(PositionType.TAI_WEI);
        request.setGrabStartTime(LocalDateTime.of(2025, 8, 11, 23, 0, 0)); // 开始时间晚于结束时间
        request.setGrabEndTime(LocalDateTime.of(2025, 8, 11, 10, 0, 0));
        request.setDutyDate(LocalDate.of(2025, 8, 12));

        Dynasty dynasty = new Dynasty();
        dynasty.setId(dynastyId);
        dynasty.setEmperorId(userId);

        when(dynastyRepository.findById(dynastyId)).thenReturn(Optional.of(dynasty));

        // When & Then
        try (var mockedStatic = mockStatic(UserContext.class)) {
            mockedStatic.when(UserContext::getUserId).thenReturn(userId);
            
            BusinessException exception = assertThrows(BusinessException.class, 
                () -> positionGrabService.setPositionGrabTime(dynastyId, request));
            assertEquals("抢夺开始时间必须早于结束时间", exception.getMessage());
        }
    }

    @Test
    void setPositionGrabTime_DifferentDays_ThrowsException() {
        // Given
        SetPositionGrabTimeRequest request = new SetPositionGrabTimeRequest();
        request.setPositionType(PositionType.TAI_WEI);
        request.setGrabStartTime(LocalDateTime.of(2025, 8, 11, 10, 0, 0));
        request.setGrabEndTime(LocalDateTime.of(2025, 8, 12, 10, 0, 0)); // 不同天
        request.setDutyDate(LocalDate.of(2025, 8, 13));

        Dynasty dynasty = new Dynasty();
        dynasty.setId(dynastyId);
        dynasty.setEmperorId(userId);

        when(dynastyRepository.findById(dynastyId)).thenReturn(Optional.of(dynasty));

        // When & Then
        try (var mockedStatic = mockStatic(UserContext.class)) {
            mockedStatic.when(UserContext::getUserId).thenReturn(userId);
            
            BusinessException exception = assertThrows(BusinessException.class, 
                () -> positionGrabService.setPositionGrabTime(dynastyId, request));
            assertEquals("抢夺开始时间和结束时间必须在同一天", exception.getMessage());
        }
    }

    @Test
    void setPositionGrabTime_DutyDateNotAfterGrabDate_ThrowsException() {
        // Given
        SetPositionGrabTimeRequest request = new SetPositionGrabTimeRequest();
        request.setPositionType(PositionType.TAI_WEI);
        request.setGrabStartTime(LocalDateTime.of(2025, 8, 11, 10, 0, 0));
        request.setGrabEndTime(LocalDateTime.of(2025, 8, 11, 23, 0, 0));
        request.setDutyDate(LocalDate.of(2025, 8, 11)); // 任职日期不在抢夺时间之后

        Dynasty dynasty = new Dynasty();
        dynasty.setId(dynastyId);
        dynasty.setEmperorId(userId);

        when(dynastyRepository.findById(dynastyId)).thenReturn(Optional.of(dynasty));

        // When & Then
        try (var mockedStatic = mockStatic(UserContext.class)) {
            mockedStatic.when(UserContext::getUserId).thenReturn(userId);
            
            BusinessException exception = assertThrows(BusinessException.class, 
                () -> positionGrabService.setPositionGrabTime(dynastyId, request));
            assertEquals("任职日期必须在抢夺时间之后", exception.getMessage());
        }
    }

    @Test
    void grabPosition_Success() {
        // Given
        GrabPositionRequest request = new GrabPositionRequest();
        request.setPositionType(PositionType.TAI_WEI);
        request.setTimeSlot(10);

        GameAccount account = new GameAccount();
        account.setId(accountId);
        account.setUserId(userId);
        account.setDynastyId(dynastyId);

        Dynasty dynasty = new Dynasty();
        dynasty.setId(dynastyId);
        dynasty.setGrabEnabled(true);

        DynastyPosition position = new DynastyPosition();
        position.setDynastyId(dynastyId);
        position.setPositionType(PositionType.TAI_WEI);
        position.setGrabStartTime(LocalDateTime.now().minusHours(1)); // 1小时前开始
        position.setGrabEndTime(LocalDateTime.now().plusHours(1)); // 1小时后结束
        position.setDutyDate(LocalDate.now().plusDays(1));
        position.setDisabledTimeSlots("");

        PositionGrab savedGrab = new PositionGrab();
        savedGrab.setId(1L);
        savedGrab.setDynastyId(dynastyId);
        savedGrab.setPositionType(PositionType.TAI_WEI);
        savedGrab.setTimeSlot(10);
        savedGrab.setAccountId(accountId);

        when(gameAccountRepository.findById(accountId)).thenReturn(Optional.of(account));
        when(dynastyRepository.findById(dynastyId)).thenReturn(Optional.of(dynasty));
        when(dynastyPositionRepository.findByDynastyIdAndPositionType(dynastyId, PositionType.TAI_WEI))
                .thenReturn(Optional.of(position));
        when(positionGrabRepository.existsByAccountIdAndDynastyIdAndPositionTypeAndDutyDate(
                accountId, dynastyId, PositionType.TAI_WEI, position.getDutyDate())).thenReturn(false);
        when(positionGrabRepository.existsByDynastyIdAndPositionTypeAndDutyDateAndTimeSlot(
                dynastyId, PositionType.TAI_WEI, position.getDutyDate(), 10)).thenReturn(false);
        when(positionGrabRepository.save(any(PositionGrab.class))).thenReturn(savedGrab);

        // When
        try (var mockedStatic = mockStatic(UserContext.class)) {
            mockedStatic.when(UserContext::getUserId).thenReturn(userId);
            PositionGrab result = positionGrabService.grabPosition(dynastyId, accountId, request);

            // Then
            assertNotNull(result);
            assertEquals(dynastyId, result.getDynastyId());
            assertEquals(PositionType.TAI_WEI, result.getPositionType());
            assertEquals(10, result.getTimeSlot());
            assertEquals(accountId, result.getAccountId());
            verify(positionGrabRepository).save(any(PositionGrab.class));
        }
    }

    @Test
    void grabPosition_AccountNotBelongsToUser_ThrowsException() {
        // Given
        GrabPositionRequest request = new GrabPositionRequest();
        request.setPositionType(PositionType.TAI_WEI);
        request.setTimeSlot(10);

        GameAccount account = new GameAccount();
        account.setId(accountId);
        account.setUserId(2L); // 不同的用户ID

        when(gameAccountRepository.findById(accountId)).thenReturn(Optional.of(account));

        // When & Then
        try (var mockedStatic = mockStatic(UserContext.class)) {
            mockedStatic.when(UserContext::getUserId).thenReturn(userId);
            
            BusinessException exception = assertThrows(BusinessException.class, 
                () -> positionGrabService.grabPosition(dynastyId, accountId, request));
            assertEquals("无权操作此账号", exception.getMessage());
        }
    }

    @Test
    void grabPosition_AccountNotInDynasty_ThrowsException() {
        // Given
        GrabPositionRequest request = new GrabPositionRequest();
        request.setPositionType(PositionType.TAI_WEI);
        request.setTimeSlot(10);

        GameAccount account = new GameAccount();
        account.setId(accountId);
        account.setUserId(userId);
        account.setDynastyId(2L); // 不同的王朝ID

        when(gameAccountRepository.findById(accountId)).thenReturn(Optional.of(account));

        // When & Then
        try (var mockedStatic = mockStatic(UserContext.class)) {
            mockedStatic.when(UserContext::getUserId).thenReturn(userId);
            
            BusinessException exception = assertThrows(BusinessException.class, 
                () -> positionGrabService.grabPosition(dynastyId, accountId, request));
            assertEquals("账号不属于该王朝", exception.getMessage());
        }
    }

    @Test
    void grabPosition_GrabNotEnabled_ThrowsException() {
        // Given
        GrabPositionRequest request = new GrabPositionRequest();
        request.setPositionType(PositionType.TAI_WEI);
        request.setTimeSlot(10);

        GameAccount account = new GameAccount();
        account.setId(accountId);
        account.setUserId(userId);
        account.setDynastyId(dynastyId);

        Dynasty dynasty = new Dynasty();
        dynasty.setId(dynastyId);
        dynasty.setGrabEnabled(false); // 未开启抢夺

        when(gameAccountRepository.findById(accountId)).thenReturn(Optional.of(account));
        when(dynastyRepository.findById(dynastyId)).thenReturn(Optional.of(dynasty));

        // When & Then
        try (var mockedStatic = mockStatic(UserContext.class)) {
            mockedStatic.when(UserContext::getUserId).thenReturn(userId);
            
            BusinessException exception = assertThrows(BusinessException.class, 
                () -> positionGrabService.grabPosition(dynastyId, accountId, request));
            assertEquals("王朝未开启官职抢夺", exception.getMessage());
        }
    }

    @Test
    void grabPosition_OutsideGrabTime_ThrowsException() {
        // Given
        GrabPositionRequest request = new GrabPositionRequest();
        request.setPositionType(PositionType.TAI_WEI);
        request.setTimeSlot(10);

        GameAccount account = new GameAccount();
        account.setId(accountId);
        account.setUserId(userId);
        account.setDynastyId(dynastyId);

        Dynasty dynasty = new Dynasty();
        dynasty.setId(dynastyId);
        dynasty.setGrabEnabled(true);

        DynastyPosition position = new DynastyPosition();
        position.setDynastyId(dynastyId);
        position.setPositionType(PositionType.TAI_WEI);
        position.setGrabStartTime(LocalDateTime.now().plusHours(1)); // 1小时后开始
        position.setGrabEndTime(LocalDateTime.now().plusHours(2)); // 2小时后结束
        position.setDutyDate(LocalDate.now().plusDays(1));

        when(gameAccountRepository.findById(accountId)).thenReturn(Optional.of(account));
        when(dynastyRepository.findById(dynastyId)).thenReturn(Optional.of(dynasty));
        when(dynastyPositionRepository.findByDynastyIdAndPositionType(dynastyId, PositionType.TAI_WEI))
                .thenReturn(Optional.of(position));

        // When & Then
        try (var mockedStatic = mockStatic(UserContext.class)) {
            mockedStatic.when(UserContext::getUserId).thenReturn(userId);
            
            BusinessException exception = assertThrows(BusinessException.class, 
                () -> positionGrabService.grabPosition(dynastyId, accountId, request));
            assertEquals("当前时间不在抢夺时间范围内", exception.getMessage());
        }
    }

    @Test
    void grabPosition_TimeSlotDisabled_ThrowsException() {
        // Given
        GrabPositionRequest request = new GrabPositionRequest();
        request.setPositionType(PositionType.TAI_WEI);
        request.setTimeSlot(10);

        GameAccount account = new GameAccount();
        account.setId(accountId);
        account.setUserId(userId);
        account.setDynastyId(dynastyId);

        Dynasty dynasty = new Dynasty();
        dynasty.setId(dynastyId);
        dynasty.setGrabEnabled(true);

        DynastyPosition position = new DynastyPosition();
        position.setDynastyId(dynastyId);
        position.setPositionType(PositionType.TAI_WEI);
        position.setGrabStartTime(LocalDateTime.now().minusHours(1));
        position.setGrabEndTime(LocalDateTime.now().plusHours(1));
        position.setDutyDate(LocalDate.now().plusDays(1));
        position.setDisabledTimeSlots("10,11,12"); // 时段10被禁用

        when(gameAccountRepository.findById(accountId)).thenReturn(Optional.of(account));
        when(dynastyRepository.findById(dynastyId)).thenReturn(Optional.of(dynasty));
        when(dynastyPositionRepository.findByDynastyIdAndPositionType(dynastyId, PositionType.TAI_WEI))
                .thenReturn(Optional.of(position));

        // When & Then
        try (var mockedStatic = mockStatic(UserContext.class)) {
            mockedStatic.when(UserContext::getUserId).thenReturn(userId);
            
            BusinessException exception = assertThrows(BusinessException.class, 
                () -> positionGrabService.grabPosition(dynastyId, accountId, request));
            assertEquals("该时段已被禁用，无法抢夺", exception.getMessage());
        }
    }

    @Test
    void grabPosition_TimeSlotAlreadyTaken_ThrowsException() {
        // Given
        GrabPositionRequest request = new GrabPositionRequest();
        request.setPositionType(PositionType.TAI_WEI);
        request.setTimeSlot(10);

        GameAccount account = new GameAccount();
        account.setId(accountId);
        account.setUserId(userId);
        account.setDynastyId(dynastyId);

        Dynasty dynasty = new Dynasty();
        dynasty.setId(dynastyId);
        dynasty.setGrabEnabled(true);

        DynastyPosition position = new DynastyPosition();
        position.setDynastyId(dynastyId);
        position.setPositionType(PositionType.TAI_WEI);
        position.setGrabStartTime(LocalDateTime.now().minusHours(1));
        position.setGrabEndTime(LocalDateTime.now().plusHours(1));
        position.setDutyDate(LocalDate.now().plusDays(1));
        position.setDisabledTimeSlots("");

        when(gameAccountRepository.findById(accountId)).thenReturn(Optional.of(account));
        when(dynastyRepository.findById(dynastyId)).thenReturn(Optional.of(dynasty));
        when(dynastyPositionRepository.findByDynastyIdAndPositionType(dynastyId, PositionType.TAI_WEI))
                .thenReturn(Optional.of(position));
        when(positionGrabRepository.existsByAccountIdAndDynastyIdAndPositionTypeAndDutyDate(
                accountId, dynastyId, PositionType.TAI_WEI, position.getDutyDate())).thenReturn(false);
        when(positionGrabRepository.existsByDynastyIdAndPositionTypeAndDutyDateAndTimeSlot(
                dynastyId, PositionType.TAI_WEI, position.getDutyDate(), 10)).thenReturn(true); // 时段已被抢夺

        // When & Then
        try (var mockedStatic = mockStatic(UserContext.class)) {
            mockedStatic.when(UserContext::getUserId).thenReturn(userId);

            BusinessException exception = assertThrows(BusinessException.class,
                () -> positionGrabService.grabPosition(dynastyId, accountId, request));
            assertEquals("该时段的官职已被夺走", exception.getMessage());
        }
    }

    @Test
    void grabPosition_UserAlreadyGrabbedInSamePositionType_ThrowsException() {
        // Given
        GrabPositionRequest request = new GrabPositionRequest();
        request.setPositionType(PositionType.TAI_WEI);
        request.setTimeSlot(10);

        GameAccount account = new GameAccount();
        account.setId(accountId);
        account.setUserId(userId);
        account.setDynastyId(dynastyId);

        Dynasty dynasty = new Dynasty();
        dynasty.setId(dynastyId);
        dynasty.setGrabEnabled(true);

        DynastyPosition position = new DynastyPosition();
        position.setDynastyId(dynastyId);
        position.setPositionType(PositionType.TAI_WEI);
        position.setGrabStartTime(LocalDateTime.now().minusHours(1));
        position.setGrabEndTime(LocalDateTime.now().plusHours(1));
        position.setDutyDate(LocalDate.now().plusDays(1));
        position.setDisabledTimeSlots("");

        when(gameAccountRepository.findById(accountId)).thenReturn(Optional.of(account));
        when(dynastyRepository.findById(dynastyId)).thenReturn(Optional.of(dynasty));
        when(dynastyPositionRepository.findByDynastyIdAndPositionType(dynastyId, PositionType.TAI_WEI))
                .thenReturn(Optional.of(position));
        when(positionGrabRepository.existsByAccountIdAndDynastyIdAndPositionTypeAndDutyDate(
                accountId, dynastyId, PositionType.TAI_WEI, position.getDutyDate())).thenReturn(true); // 用户已抢夺

        // When & Then
        try (var mockedStatic = mockStatic(UserContext.class)) {
            mockedStatic.when(UserContext::getUserId).thenReturn(userId);

            BusinessException exception = assertThrows(BusinessException.class,
                () -> positionGrabService.grabPosition(dynastyId, accountId, request));
            assertEquals("在该官职类型下已经抢夺了一个时段了，不要太贪心！", exception.getMessage());
        }
    }
}
