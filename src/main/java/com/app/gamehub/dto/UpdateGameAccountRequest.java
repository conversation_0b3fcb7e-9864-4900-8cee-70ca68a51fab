package com.app.gamehub.dto;

import com.app.gamehub.entity.GameAccount;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(description = "更新游戏账号请求")
public class UpdateGameAccountRequest {

  @Schema(description = "账号名称")
  private String accountName;

  @PositiveOrZero(message = "战力值不能为负数")
  @Schema(description = "战力值")
  private Long powerValue;

  @DecimalMin(value = "0.00", message = "伤害加成不能为负数")
  @DecimalMax(value = "999.99", message = "伤害加成不能超过999.99")
  @Digits(integer = 3, fraction = 2, message = "伤害加成最多3位整数2位小数")
  @Schema(description = "伤害加成")
  private BigDecimal damageBonus;

  @Min(value = 1, message = "兵等级最小为1")
  @Max(value = 30, message = "兵等级最大为30")
  @Schema(description = "兵等级")
  private Integer troopLevel;

  @PositiveOrZero(message = "集结容量不能为负数")
  @Schema(description = "集结容量（万）")
  private Integer rallyCapacity;

  @Schema(description = "成员等级")
  private GameAccount.MemberTier memberTier;
}
