package com.app.gamehub.dto;

import com.app.gamehub.entity.WarType;
import com.app.gamehub.model.WarTactic;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class UseTacticRequest {

  @NotNull(message = "战事类型不能为空")
  @Schema(description = "战术使用在哪一种战事类型上", example = "GUANDU_ONE")
  private WarType warType;

  @NotNull(message = "战术类型不能为空")
  @Schema(description = "战术类型", example = "HOLD_GRAIN")
  private WarTactic tactic;
}
