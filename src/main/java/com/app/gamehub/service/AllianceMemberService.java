package com.app.gamehub.service;

import com.app.gamehub.dto.JoinAllianceRequest;
import com.app.gamehub.entity.Alliance;
import com.app.gamehub.entity.AllianceApplication;
import com.app.gamehub.entity.GameAccount;
import com.app.gamehub.exception.BusinessException;
import com.app.gamehub.repository.AllianceApplicationRepository;
import com.app.gamehub.repository.AllianceRepository;
import com.app.gamehub.repository.GameAccountRepository;
import com.app.gamehub.repository.WarApplicationRepository;
import com.app.gamehub.repository.WarArrangementRepository;
import com.app.gamehub.util.UserContext;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class AllianceMemberService {

  private final AllianceRepository allianceRepository;
  private final GameAccountRepository gameAccountRepository;
  private final AllianceApplicationRepository applicationRepository;
  private final WarApplicationRepository warApplicationRepository;
  private final WarArrangementRepository warArrangementRepository;

  @Transactional
  public AllianceApplication applyToJoinAlliance(JoinAllianceRequest request) {
    Long accountId = request.getAccountId();

    // 验证账号是否存在且属于当前用户
    GameAccount account =
        gameAccountRepository
            .findById(accountId)
            .orElseThrow(() -> new BusinessException("游戏账号不存在"));

    if (!account.getUserId().equals(UserContext.getUserId())) {
      throw new BusinessException("只能为自己的账号申请加入联盟");
    }

    // 验证账号是否已加入联盟
    if (account.getAllianceId() != null) {
      throw new BusinessException("账号已加入联盟，请先退出当前联盟");
    }

    // 查找联盟
    Alliance alliance =
        allianceRepository
            .findByCode(request.getAllianceCode().toUpperCase())
            .orElseThrow(() -> new BusinessException("联盟不存在"));

    // 验证账号和联盟是否在同一个区
    if (!account.getServerId().equals(alliance.getServerId())) {
      throw new BusinessException("只能申请加入同一个区的联盟");
    }

    // 检查是否已有待处理的申请
    if (applicationRepository.existsByAccountIdAndStatus(
        accountId, AllianceApplication.ApplicationStatus.PENDING)) {
      throw new BusinessException("已有待处理的申请，请等待处理结果");
    }

    // 检查联盟成员数量
    long memberCount = allianceRepository.countMembersByAllianceId(alliance.getId());
    if (memberCount >= 100) {
      throw new BusinessException("联盟成员已满，无法申请加入");
    }

    // 创建申请
    AllianceApplication application = new AllianceApplication();
    application.setAccountId(accountId);
    application.setAllianceId(alliance.getId());
    application.setStatus(AllianceApplication.ApplicationStatus.PENDING);

    return applicationRepository.save(application);
  }

  @Transactional
  public AllianceApplication processApplication(Long applicationId, boolean approved) {
    Long userId = UserContext.getUserId();
    AllianceApplication application =
        applicationRepository
            .findById(applicationId)
            .orElseThrow(() -> new BusinessException("申请不存在"));

    // 验证是否为盟主
    Alliance alliance =
        allianceRepository
            .findById(application.getAllianceId())
            .orElseThrow(() -> new BusinessException("联盟不存在"));

    if (!alliance.getLeaderId().equals(userId)) {
      throw new BusinessException("只有盟主可以处理申请");
    }

    // 验证申请状态
    if (application.getStatus() != AllianceApplication.ApplicationStatus.PENDING) {
      throw new BusinessException("申请已被处理");
    }

    if (approved) {
      // 再次检查联盟成员数量
      long memberCount = allianceRepository.countMembersByAllianceId(alliance.getId());
      if (memberCount >= 100) {
        throw new BusinessException("联盟成员已满，无法通过申请");
      }

      // 通过申请，将账号加入联盟
      GameAccount account =
          gameAccountRepository
              .findById(application.getAccountId())
              .orElseThrow(() -> new BusinessException("游戏账号不存在"));

      account.setAllianceId(alliance.getId());
      account.setMemberTier(GameAccount.MemberTier.TIER_1); // 默认为一阶成员
      gameAccountRepository.save(account);

      application.setStatus(AllianceApplication.ApplicationStatus.APPROVED);
    } else {
      application.setStatus(AllianceApplication.ApplicationStatus.REJECTED);
    }

    application.setProcessedBy(userId);
    return applicationRepository.save(application);
  }

  @Transactional
  public void removeMember(Long accountId) {
    Long userId = UserContext.getUserId();
    GameAccount account =
        gameAccountRepository
            .findById(accountId)
            .orElseThrow(() -> new BusinessException("游戏账号不存在"));

    if (account.getAllianceId() == null) {
      throw new BusinessException("账号未加入任何联盟");
    }

    // 验证是否为盟主
    Alliance alliance =
        allianceRepository
            .findById(account.getAllianceId())
            .orElseThrow(() -> new BusinessException("联盟不存在"));

    if (!alliance.getLeaderId().equals(userId) && account.getUserId().equals(userId)) {
      throw new BusinessException("只有盟主或账号本人可以操作账号退出联盟");
    }

    // 移除成员
    account.setAllianceId(null);
    account.setMemberTier(null);
    applicationRepository.deleteAllByAccountId(accountId);
    warApplicationRepository.deleteAllByAccountId(accountId);
    warArrangementRepository.deleteAllByAccountId(accountId);
    gameAccountRepository.save(account);
  }

  public List<AllianceApplication> getPendingApplications(Long allianceId) {
    Long userId = UserContext.getUserId();
    // 验证是否为盟主
    Alliance alliance =
        allianceRepository.findById(allianceId).orElseThrow(() -> new BusinessException("联盟不存在"));

    if (!alliance.getLeaderId().equals(userId)) {
      throw new BusinessException("只有盟主可以查看申请列表");
    }

    return applicationRepository.findByAllianceIdAndStatusOrderByCreatedAtAsc(
        allianceId, AllianceApplication.ApplicationStatus.PENDING);
  }

  public List<AllianceApplication> getAccountApplications(Long accountId) {
    return applicationRepository.findByAccountIdOrderByCreatedAtDesc(accountId);
  }

  public List<GameAccount> getAllianceMembers(Long allianceId) {
    // 验证联盟是否存在
    allianceRepository.findById(allianceId).orElseThrow(() -> new BusinessException("联盟不存在"));

    // 获取联盟成员列表（按战力值降序排列）
    return gameAccountRepository.findByAllianceIdOrderByPowerValueDesc(allianceId);
  }
}
