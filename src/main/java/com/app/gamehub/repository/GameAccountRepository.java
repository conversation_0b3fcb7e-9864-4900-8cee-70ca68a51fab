package com.app.gamehub.repository;

import com.app.gamehub.entity.GameAccount;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface GameAccountRepository extends JpaRepository<GameAccount, Long> {

  List<GameAccount> findByUserIdOrderByServerIdDesc(Long userId);

  List<GameAccount> findByAllianceIdOrderByPowerValueDesc(Long allianceId);

  List<GameAccount> findByUserIdAndServerId(Long userId, Integer serverId);

  long countByUserIdAndServerId(Long userId, Integer serverId);

  long countByAllianceId(Long allianceId);

  boolean existsByUserIdAndServerIdAndAllianceIdIsNotNull(Long userId, Integer serverId);

  /** 根据王朝ID查找成员列表，按战力值降序排序 */
  List<GameAccount> findByDynastyIdOrderByPowerValueDesc(Long dynastyId);

  /** 统计王朝成员数量 */
  long countByDynastyId(Long dynastyId);

  /** 检查账号是否已加入王朝 */
  boolean existsByUserIdAndServerIdAndDynastyIdIsNotNull(Long userId, Integer serverId);

  /** 批量清空指定王朝的所有成员的王朝关联 */
  @Modifying
  @Query("UPDATE GameAccount ga SET ga.dynastyId = null WHERE ga.dynastyId = :dynastyId")
  void clearDynastyIdByDynastyId(@Param("dynastyId") Long dynastyId);

  /** 批量清空指定联盟的所有成员的联盟关联 */
  @Modifying
  @Query("UPDATE GameAccount ga SET ga.allianceId = null, ga.memberTier = null WHERE ga.allianceId = :allianceId")
  void clearAllianceIdByAllianceId(@Param("allianceId") Long allianceId);
}
