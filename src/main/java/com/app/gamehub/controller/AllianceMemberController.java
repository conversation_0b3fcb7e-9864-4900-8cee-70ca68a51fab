package com.app.gamehub.controller;

import com.app.gamehub.dto.ApiResponse;
import com.app.gamehub.dto.JoinAllianceRequest;
import com.app.gamehub.dto.ProcessApplicationRequest;
import com.app.gamehub.entity.AllianceApplication;
import com.app.gamehub.entity.GameAccount;
import com.app.gamehub.service.AllianceMemberService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/alliance-members")
@Tag(name = "联盟成员管理", description = "联盟成员相关接口")
public class AllianceMemberController {

  private final AllianceMemberService allianceMemberService;

  public AllianceMemberController(AllianceMemberService allianceMemberService) {
    this.allianceMemberService = allianceMemberService;
  }

  @PostMapping("/apply")
  @Operation(summary = "申请加入联盟")
  public ApiResponse<AllianceApplication> applyToJoinAlliance(
      @Valid @RequestBody JoinAllianceRequest request) {
    AllianceApplication application = allianceMemberService.applyToJoinAlliance(request);
    return ApiResponse.success("申请提交成功", application);
  }

  @PostMapping("/applications/{applicationId}/process")
  @Operation(summary = "处理加入联盟申请")
  public ApiResponse<AllianceApplication> processApplication(
      @Parameter(description = "申请ID", example = "1") @PathVariable Long applicationId,
      @Valid @RequestBody ProcessApplicationRequest request) {

    AllianceApplication application =
        allianceMemberService.processApplication(applicationId, request.getApproved());
    return ApiResponse.success("申请处理成功", application);
  }

  @DeleteMapping("/accounts/{accountId}")
  @Operation(summary = "移除联盟成员")
  public ApiResponse<Void> removeMember(
      @Parameter(description = "账号ID", example = "1") @PathVariable Long accountId) {
    allianceMemberService.removeMember(accountId);
    return ApiResponse.success("成员移除成功", null);
  }

  @GetMapping("/alliances/{allianceId}/applications")
  @Operation(summary = "获取联盟待处理申请列表")
  public ApiResponse<List<AllianceApplication>> getPendingApplications(
      @Parameter(description = "联盟ID", example = "1") @PathVariable Long allianceId) {
    List<AllianceApplication> applications =
        allianceMemberService.getPendingApplications(allianceId);
    return ApiResponse.success(applications);
  }

  @GetMapping("/accounts/{accountId}/applications")
  @Operation(summary = "获取账号申请历史")
  public ApiResponse<List<AllianceApplication>> getAccountApplications(
      @Parameter(description = "账号ID", example = "1") @PathVariable Long accountId) {
    List<AllianceApplication> applications =
        allianceMemberService.getAccountApplications(accountId);
    return ApiResponse.success(applications);
  }

  @GetMapping("/alliances/{allianceId}/members")
  @Operation(summary = "获取联盟成员列表")
  public ApiResponse<List<GameAccount>> getAllianceMembers(
      @Parameter(description = "联盟ID", example = "1") @PathVariable Long allianceId) {
    List<GameAccount> members = allianceMemberService.getAllianceMembers(allianceId);
    return ApiResponse.success(members);
  }
}
