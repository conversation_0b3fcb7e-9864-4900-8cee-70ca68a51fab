package com.app.gamehub.task;

import com.app.gamehub.entity.DynastyPosition;
import com.app.gamehub.repository.DynastyPositionRepository;
import com.app.gamehub.service.PositionGrabService;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
public class CleanData {

  private final PositionGrabService positionGrabService;
  private final DynastyPositionRepository dynastyPositionRepository;

  /** 定时清理已结束抢夺活动的锁 每小时执行一次，清理结束时间在当前时间之前的抢夺活动对应的锁 */
  @Scheduled(cron = "0 0 * * * ?")
  public void cleanPositionGrabLock() {
    try {
      LocalDateTime now = LocalDateTime.now();

      // 查找已结束的抢夺活动（结束时间在当前时间之前）
      List<DynastyPosition> endedPositions = dynastyPositionRepository.findByGrabEndTimeBefore(now);

      if (endedPositions.isEmpty()) {
        log.debug("没有找到需要清理锁的已结束抢夺活动");
        return;
      }

      Set<Long> dynastyIds =
          endedPositions.stream().map(DynastyPosition::getDynastyId).collect(Collectors.toSet());

      log.info("开始清理 {} 个王朝的抢夺锁，王朝ID: {}", dynastyIds.size(), dynastyIds);

      int removedCount = positionGrabService.removeLock(dynastyIds);

      log.info("成功清理了 {} 个抢夺锁", removedCount);

    } catch (Exception e) {
      log.error("清理抢夺锁时发生异常", e);
    }
  }

  /** 定时清理王朝官职配置 */
  @Transactional
  @Scheduled(cron = "0 0 3 * * ?")
  public void cleanDynastyPosition() {
    try {
      LocalDateTime now = LocalDateTime.now();
      LocalDate localDate = LocalDate.now();
      List<DynastyPosition> endedPositions =
          dynastyPositionRepository.findByGrabEndTimeBeforeAndDutyDateBefore(
              now, localDate.minusDays(15));
      if (!endedPositions.isEmpty()) {
        dynastyPositionRepository.deleteAll(endedPositions);
        log.info("成功清理了 {} 个王朝官职配置", endedPositions.size());
      }
    } catch (Exception e) {
      log.error("清理王朝官职配置时发生异常", e);
    }
  }
}
