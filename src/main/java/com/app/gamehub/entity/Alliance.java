package com.app.gamehub.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "alliances")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class Alliance extends BaseEntity {

  @Column(name = "name", nullable = false)
  private String name;

  @Column(name = "code", unique = true, nullable = false, length = 6)
  private String code;

  @Column(name = "server_id", nullable = false)
  private Integer serverId;

  @Column(name = "leader_id", nullable = false)
  private Long leaderId;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "leader_id", insertable = false, updatable = false)
  private User leader;
}
