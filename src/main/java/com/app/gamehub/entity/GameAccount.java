package com.app.gamehub.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "game_accounts")
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class GameAccount extends BaseEntity {

  @Column(name = "user_id", nullable = false)
  private Long userId;

  @Column(name = "server_id", nullable = false)
  private Integer serverId;

  @Column(name = "account_name", nullable = false)
  private String accountName;

  @Column(name = "power_value", nullable = false)
  private Long powerValue;

  @Column(name = "damage_bonus", precision = 5, scale = 2)
  private BigDecimal damageBonus;

  @Column(name = "troop_level")
  private Integer troopLevel;

  @Column(name = "rally_capacity")
  private Integer rallyCapacity;

  @Column(name = "alliance_id")
  private Long allianceId;

  @Column(name = "dynasty_id")
  private Long dynastyId;

  @Column(name = "member_tier")
  @Enumerated(EnumType.STRING)
  private MemberTier memberTier;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "user_id", insertable = false, updatable = false)
  private User user;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "alliance_id", insertable = false, updatable = false)
  private Alliance alliance;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "dynasty_id", insertable = false, updatable = false)
  private Dynasty dynasty;

  public enum MemberTier {
    TIER_1,
    TIER_2,
    TIER_3,
    TIER_4,
    TIER_5
  }
}
