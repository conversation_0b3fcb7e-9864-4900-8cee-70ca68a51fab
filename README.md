# 游戏战术室

这是一个游戏战术室（微信小程序）的后端项目，支持多种游戏，玩家可以在这个系统上制定战术。


## 系统信息

1. 实体的主键id使用雪花算法生成、所有Long字段返回前端的时候都转为String，使用jackson自定义序列化实现
2. 使用 swagger 展示接口信息，swagger的接口信息中，Long要使用String代替

> 目前支持的游戏有：三国·冰河时代

## 三国·冰河时代

在这个游戏中，有多个区，每个区有独一无二的数字区号，从1开始，每个小程序用户可以在每个区中创建两个账号，可以在每个区中创建多个联盟，每个账号只能加入一个联盟，每个联盟的正式成员总数不能超过100人。

1. 在联盟中，联盟包含联盟所在的区号（从1开始的整数），联盟名称，联盟成员有价位区分，分为一阶至五阶
2. 每个账号有账号所在的区号，账号名称、战力值、伤害加成（保留两位小数的数字，比如12.14、16.35这类数字）、兵等级（整数，1-30）、集结容量（出征时兵的集结总数，以万为单位）
3. 联盟有战事安排，现在有四种战事，分别为：官渡一、官渡二、攻城、守城，联盟成员只能申请加入官渡一或者官渡二，不能同时申请官渡一和官渡二，攻城和守城由盟主安排，不需要成员申请

现在需要以下功能：

1. 微信小程序用户登录、查询小程序用户信息
2. 除登录接口以外的其他接口使用 JWT 认证
3. 用户创建联盟（生成全局唯一的六位由英文数字组合的联盟编码）、更新（更新联盟名称、联盟编码）、删除联盟、转交联盟（将盟主转交到其他小程序用户）
4. 用户创建、更新、删除游戏账号
5. 小程序用户能查询自己创建的所有联盟和所有账号（接口的返回数据中使用两个字段展示，一个字段展示创建的所有联盟集合，一个字段展示创建的所有账号集合，根据区区号进行降序排序）
6. 账号申请加入联盟（通过联盟编码加入申请），创建的某个区的游戏账号只能申请加入同一个区的联盟
7. 盟主可以通过或者拒绝账号加入联盟的申请，如果通过申请，则账号成为联盟的正式成员之一
8. 盟主可以移除成员
9. 小程序用户更新账号信息（不能更新账号所在的区号）、盟主能更新盟里成员的账号信息
10. 成员可以申请加入官渡一或者官渡二战事
11. 盟主可以通过或者拒绝成员加入官渡一或者官渡二的申请
12. 盟主可以将成员的申请移动到另一个官渡战事，比如成员申请的官渡一，盟主可以将成员改为申请官渡二
13. 每一种战事下，盟主可以安排相关成员，战事下有战事分组，盟主可以安排成员进入战事分组，也可以不安排进分组，同时，盟主可以创建、更新、删除战事分组，分组包含分组名称、分组任务、分组成员
14. 盟主可以将成员添加到战事分组，也可以在同一种战事下将一个分组下的某个成员移动到另一个分组，也就是在同一种战事下，成员可以安排进分组，也可以不安排进分组，没有移动到分组的成员属于战事机动人员
15. 盟主可以清空战事中的所有成员安排
16. 可以查询每一种战事的人员安排
17. 小程序用户可以查询账号申请加入联盟的状态、可以查询账号申请加入官渡一或者官渡二的状态
18. 可以查询联盟的详细信息，包含联盟基本信息和所有战事的人员安排
19. 盟主可以查询申请加入联盟的账号列表、查询申请加入官渡一和官渡二的申请列表
20. 每个用户在每个区都能且只能创建一个王朝，创建王朝的时候生成王朝编码，创建王朝的用户就是这个王朝的天子
21. 每个账号都只能加入同区的一个王朝（使用王朝编码加入），加入后就是这个王朝的成员，只需要有王朝编码就能加入王朝
22. 每个王朝都有“太尉”和“尚书令”两种官职，每一种官职都有24个时间段（00:00:00~00:59:59、01:00:00~01:59:59等），每个时段只能有一个王朝成员任职，需要王朝成员抢夺，如果某个时段被某个王朝成员抢了，那么其他成员无法抢夺（需要考虑多个成员在同一时间抢夺同一个时段的官职的情况）
23. 天子可以设置每种官职可以开始抢夺的开始时间和结束的时间（年月日小时分钟秒），抢夺的官职的任职日期（年月日），抢夺的时间必须早于任职日期，比如设置的开始抢夺时间是：2025年8月11日 10:00:00到2025年8月11日 23:00:00（结束时间必须跟开始时间处于同一天），抢夺的官职的任职日期必须在2025年8月11日之后，比如2025年8月12日，那么成员就可以在2025年8月11日 10:00:00到2025年8月11日 23:00:00之间抢夺2025年8月12日每种官职的24个时段的任意一个时段
24. 天子可以设置官职的哪些时段无法被抢夺，可以关闭或开启所有官职的抢夺，可以清空抢夺的结果，同时，系统不需要保留历史抢夺的结果数据，在天子设置每一种官职抢夺的开始时间和抢夺的任职时间之后清空王朝该官职的抢夺结果的历史数据
25. 天子和王朝成员都可以查看所有官职的抢夺结果
